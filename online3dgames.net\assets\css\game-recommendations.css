/* Game Recommendations Styles */
:root {
    --hex-size: 120px;
}

.game-recommendations {
    max-width: 1200px;
    margin: 40px auto;
    padding: 20px;
    border-radius: 15px;
}

.game-recommendations.similar-games {
    margin-bottom: 20px;
}

.game-recommendations.other-games {
    margin-top: 20px;
}

.recommendations-header {
    text-align: center;
    margin-bottom: 30px;
}

.recommendations-title {
    color: #ffffff;
    font-size: 28px;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.recommendations-subtitle {
    color: #cccccc;
    font-size: 16px;
    margin-bottom: 0;
}

.recommendations-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    max-width: 800px;
    margin: 0 auto;
}

.recommendations-row {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: calc(var(--hex-size) * -0.23);
    flex-wrap: wrap;
}

.recommendations-row:last-child {
    margin-bottom: 0;
}

.recommendation-card {
    width: calc(var(--hex-size) * 0.8);
    height: calc(var(--hex-size) * 0.8 * 1.1547);
    position: relative;
    text-decoration: none;
    color: inherit;
    cursor: pointer;
    z-index: 1;
    transition: all 0.3s ease;
}

.recommendation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--card-primary, #667eea), var(--card-secondary, #764ba2));
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    z-index: -1;
    transition: all 0.3s ease;
}

.recommendation-card:hover {
    z-index: 10;
    transform: scale(1.1);
}

.recommendation-card:hover::before {
    filter: brightness(1.2) saturate(1.3);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.recommendation-card .card-image {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.recommendation-card .game-icon {
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.recommendation-card .game-name {
    font-size: 12px;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    line-height: 1.2;
    margin: 0;
}

.recommendation-card .game-rating {
    font-size: 0.7rem;
    color: #ffeb3b;
    margin-top: 4px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* Color variations for different game types */
.recommendation-card[data-category="blackjack"] {
    --card-primary: #d32f2f;
    --card-secondary: #f57c00;
}

.recommendation-card[data-category="card-games"] {
    --card-primary: #1976d2;
    --card-secondary: #388e3c;
}

.recommendation-card[data-category="puzzle"] {
    --card-primary: #7b1fa2;
    --card-secondary: #512da8;
}

.recommendation-card[data-category="arcade"] {
    --card-primary: #f57c00;
    --card-secondary: #e64a19;
}

.recommendation-card[data-category="relaxation"] {
    --card-primary: #00796b;
    --card-secondary: #4caf50;
}

/* Mobile responsive */
@media (max-width: 768px) {
    :root {
        --hex-size: 80px;
    }

    .game-recommendations {
        margin: 20px auto;
        padding: 15px;
    }

    .game-recommendations.similar-games {
        margin-bottom: 15px;
    }

    .game-recommendations.other-games {
        margin-top: 15px;
    }

    .recommendations-title {
        font-size: 22px;
    }

    .recommendations-subtitle {
        font-size: 14px;
    }

    .recommendations-grid {
        padding: 15px 0;
    }

    .recommendation-card {
        width: calc(var(--hex-size) * 0.8);
        height: calc(var(--hex-size) * 0.8 * 1.1547);
    }

    .recommendation-card .game-icon {
        font-size: 1.6rem;
    }

    .recommendation-card .game-name {
        font-size: 0.85rem;
    }

    .recommendation-card .game-rating {
        font-size: 0.65rem;
    }
}

@media (max-width: 480px) {
    :root {
        --hex-size: 80px;
    }

    .recommendations-row {
        flex-wrap: wrap;
        justify-content: center;
    }

    .recommendation-card {
        width: calc(var(--hex-size) * 0.8);
        height: calc(var(--hex-size) * 0.8 * 1.1547);
    }

    .recommendations-title {
        font-size: 18px;
    }

    .recommendations-subtitle {
        font-size: 12px;
    }

    .recommendation-card .game-icon {
        font-size: 1.2rem;
    }

    .recommendation-card .game-name {
        font-size: 0.7rem;
        line-height: 1.0;
    }

    .recommendation-card .game-rating {
        font-size: 0.55rem;
    }

    .recommendations-grid {
        padding: 10px 0;
    }
}
