class FreeCell {
    constructor(testMode = false) {
        this.testMode = testMode;
        this.deck = [];
        this.freeCells = [null, null, null, null]; // 4 free cells
        this.foundations = { hearts: [], diamonds: [], clubs: [], spades: [] }; // 4 foundation piles
        this.tableau = [[], [], [], [], [], [], [], []]; // 8 tableau columns
        this.score = 0;
        this.moves = 0;
        this.startTime = null;
        this.timer = null;
        this.gameWon = false;
        this.moveHistory = [];

        this.comboCount = 0;
        this.lastMoveTime = 0;
        this.comboTimeWindow = 10000;
        this.maxComboMultiplier = 5;

        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElement = null;
        this.draggedElements = null;
        this.isDragging = false;
        this.justFinishedDrag = false;
        this.dragOffset = { x: 0, y: 0 };
        this.dragStartPos = { x: 0, y: 0 };
        this.dragThreshold = this.isMobile() ? 12 : 8;
        this.longPressTimer = null;
        this.longPressDelay = 300;
        this.hasAutoFullscreened = false;

        this.suits = ['spades', 'hearts', 'clubs', 'diamonds'];
        this.ranks = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K'];
        this.suitSymbols = { hearts: '♥', diamonds: '♦', clubs: '♣', spades: '♠' };
        this.suitColors = { hearts: 'red', diamonds: 'red', clubs: 'black', spades: 'black' };

        this.isAnimatingCard = false;
        this.isAutoCompleting = false;
        this.isProcessingAction = false;
        this.lastActionTime = 0;
        this.actionCooldown = 100;
        this.buttonCooldowns = new Map();
        this.consecutiveAutoMoves = 0;
        this.recentlyMovedFromFoundation = new Set();

        if (!testMode) {
            this.bindEvents();
        } else {
            this.initializeGame();
        }
    }

    isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               ('ontouchstart' in window) ||
               (navigator.maxTouchPoints > 0);
    }

    // Create a standard 52-card deck
    createDeck() {
        this.deck = [];
        for (const suit of this.suits) {
            for (const rank of this.ranks) {
                this.deck.push({
                    suit: suit,
                    rank: rank,
                    color: this.suitColors[suit],
                    value: this.getCardValue(rank),
                    id: `${rank}_${suit}`
                });
            }
        }
    }

    getCardValue(rank) {
        if (rank === 'A') return 1;
        if (rank === 'J') return 11;
        if (rank === 'Q') return 12;
        if (rank === 'K') return 13;
        return parseInt(rank);
    }

    // Shuffle the deck using Fisher-Yates algorithm
    shuffleDeck() {
        for (let i = this.deck.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.deck[i], this.deck[j]] = [this.deck[j], this.deck[i]];
        }
    }

    // Deal cards for FreeCell (all face up)
    dealCards() {
        this.createDeck();
        this.shuffleDeck();

        // Clear all areas
        this.freeCells = [null, null, null, null];
        this.foundations = { hearts: [], diamonds: [], clubs: [], spades: [] };
        this.tableau = [[], [], [], [], [], [], [], []];

        // Deal cards to tableau: first 4 columns get 7 cards, last 4 get 6 cards
        let cardIndex = 0;
        for (let col = 0; col < 8; col++) {
            const cardsInColumn = col < 4 ? 7 : 6;
            for (let row = 0; row < cardsInColumn; row++) {
                this.tableau[col].push(this.deck[cardIndex]);
                cardIndex++;
            }
        }

        // Auto-move Aces to foundations after dealing
        setTimeout(() => {
            this.autoMoveAcesOnStart();
        }, 100);
    }

    // Auto-move Aces to foundations at game start
    async autoMoveAcesOnStart() {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        let foundAces = false;

        // Check each tableau column for Aces at the bottom
        for (let col = 0; col < 8; col++) {
            const column = this.tableau[col];
            if (column.length > 0) {
                const bottomCard = column[column.length - 1];
                if (bottomCard.rank === 'A') {
                    foundAces = true;
                    await this.animateCardToFoundation(bottomCard, { type: 'tableau', index: col });
                    await new Promise(resolve => setTimeout(resolve, 200)); // Small delay between moves
                }
            }
        }

        if (foundAces) {
            this.renderGame();
            this.updateStats();
            this.updateCardStates();

            // Check for more auto-movable cards after moving Aces
            setTimeout(() => {
                this.autoMoveToFoundations();
            }, 300);
        }
    }

    // Animate card to foundation with flying animation
    async animateCardToFoundation(card, sourceInfo, isAutoMove = false) {
        this.isAnimatingCard = true;

        // Get source card element
        let $sourceCard;
        if (sourceInfo.type === 'tableau') {
            const $pile = $(`#tableau-${sourceInfo.index}`);
            $sourceCard = $pile.find('.card').last();
        } else if (sourceInfo.type === 'freecell') {
            $sourceCard = $(`#freecell-${sourceInfo.index} .card`);
        }

        if (!$sourceCard || !$sourceCard.length) {
            this.isAnimatingCard = false;
            return;
        }

        // Get target foundation position
        const $targetFoundation = $(`#foundation-${card.suit}`);
        const sourceRect = $sourceCard[0].getBoundingClientRect();
        const targetRect = $targetFoundation[0].getBoundingClientRect();

        const startPos = {
            left: sourceRect.left,
            top: sourceRect.top
        };

        // Calculate end position based on existing cards in foundation
        const foundation = this.foundations[card.suit];
        const cardOffset = foundation.length * 2; // Small offset for stacked cards

        const endPos = {
            left: targetRect.left + (targetRect.width - sourceRect.width) / 2,
            top: targetRect.top + cardOffset
        };

        // Perform the move in data
        if (sourceInfo.type === 'tableau') {
            this.tableau[sourceInfo.index].pop();
        } else if (sourceInfo.type === 'freecell') {
            this.freeCells[sourceInfo.index] = null;
        }
        this.foundations[card.suit].push(card);
        this.score += 10;
        this.moves++;

        // Calculate animation duration based on consecutive auto moves
        let duration = 400;
        if (isAutoMove && this.consecutiveAutoMoves >= 3) {
            duration = 80; // 5x faster (400/5 = 80)
        }

        // Animate the card
        await this.animateSingleCard($sourceCard, startPos, endPos, duration);

        // Update display
        this.renderGame();
        this.updateStats();
        this.updateCardStates();
        this.isAnimatingCard = false;
    }

    // Auto-move cards to foundations when possible
    async autoMoveToFoundations() {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        let foundMove = false;

        // Check tableau columns for movable cards
        for (let col = 0; col < 8; col++) {
            const column = this.tableau[col];
            if (column.length > 0) {
                const topCard = column[column.length - 1];

                // Check if this card can move to any foundation
                // Skip if this card was recently moved from foundation
                if (!this.recentlyMovedFromFoundation.has(topCard.id)) {
                    for (const suit of this.suits) {
                        if (this.canMoveToFoundation(topCard, suit)) {
                            foundMove = true;
                            this.consecutiveAutoMoves++;
                            await this.animateCardToFoundation(topCard, { type: 'tableau', index: col }, true);

                            // Adjust delay based on consecutive moves
                            const delay = this.consecutiveAutoMoves >= 3 ? 30 : 150; // 5x faster delay
                            await new Promise(resolve => setTimeout(resolve, delay));
                            break;
                        }
                    }
                }

                if (foundMove) break;
            }
        }

        // Check free cells for movable cards
        if (!foundMove) {
            for (let i = 0; i < 4; i++) {
                const card = this.freeCells[i];
                if (card) {
                    // Check if this card can move to any foundation
                    // Skip if this card was recently moved from foundation
                    if (!this.recentlyMovedFromFoundation.has(card.id)) {
                        for (const suit of this.suits) {
                            if (this.canMoveToFoundation(card, suit)) {
                                foundMove = true;
                                this.consecutiveAutoMoves++;
                                await this.animateCardToFoundation(card, { type: 'freecell', index: i }, true);

                                // Adjust delay based on consecutive moves
                                const delay = this.consecutiveAutoMoves >= 3 ? 30 : 150; // 5x faster delay
                                await new Promise(resolve => setTimeout(resolve, delay));
                                break;
                            }
                        }
                    }

                    if (foundMove) break;
                }
            }
        }

        // If we found a move, check for more moves recursively
        if (foundMove) {
            this.renderGame();
            this.updateStats();
            this.updateCardStates();

            const nextDelay = this.consecutiveAutoMoves >= 3 ? 40 : 200; // 5x faster next check
            setTimeout(() => {
                this.autoMoveToFoundations();
            }, nextDelay);
        } else {
            // Reset counter when no more moves are found
            this.consecutiveAutoMoves = 0;
            // Clear the recently moved from foundation set after auto-move cycle completes
            this.recentlyMovedFromFoundation.clear();
        }
    }

    // Initialize a new game
    initializeGame() {
        this.score = 0;
        this.moves = 0;
        this.gameWon = false;
        this.moveHistory = [];
        this.startTime = Date.now();
        this.comboCount = 0;
        this.lastMoveTime = 0;
        this.consecutiveAutoMoves = 0;
        this.recentlyMovedFromFoundation = new Set();

        this.dealCards();
        this.renderGame();
        this.startTimer();
        this.updateStats();
        this.updateCardStates();
    }

    // Render the entire game state
    renderGame() {
        this.renderFreeCells();
        this.renderFoundations();
        this.renderTableau();
    }

    // Update card states (enable/disable based on moveability)
    updateCardStates() {
        // Reset all cards to enabled state first
        $('.card').removeClass('card-disabled').css('pointer-events', 'auto');

        // Check each tableau column
        for (let col = 0; col < 8; col++) {
            const column = this.tableau[col];
            if (column.length === 0) continue;

            // Check each card in the column
            for (let cardIndex = 0; cardIndex < column.length; cardIndex++) {
                const card = column[cardIndex];
                const $cardElement = $(`#tableau-${col} .card[data-card-id="${card.id}"]`);

                if (!this.isCardMovable(card, col, cardIndex)) {
                    $cardElement.addClass('card-disabled').css('pointer-events', 'none');
                }
            }
        }

        // Check free cells
        for (let i = 0; i < 4; i++) {
            const card = this.freeCells[i];
            if (card) {
                const $cardElement = $(`#freecell-${i} .card[data-card-id="${card.id}"]`);
                if (!this.isCardMovable(card, -1, -1, 'freecell')) {
                    $cardElement.addClass('card-disabled').css('pointer-events', 'none');
                }
            }
        }

        // Check foundation cards - only the top card of each foundation can be moved
        for (const suit of this.suits) {
            const foundation = this.foundations[suit];
            if (foundation.length > 0) {
                const topCard = foundation[foundation.length - 1];
                const $cardElement = $(`#foundation-${suit} .card[data-card-id="${topCard.id}"]`);

                if (!this.isCardMovable(topCard, -1, -1, 'foundation', suit)) {
                    $cardElement.addClass('card-disabled').css('pointer-events', 'none');
                }
            }
        }
    }

    // Check if a card is movable
    isCardMovable(card, columnIndex, cardIndex, source = 'tableau', suit = null) {
        if (source === 'freecell') {
            // Free cell cards are always movable - let the user try and get feedback
            return true;
        }

        if (source === 'foundation') {
            // Foundation cards can be moved back to tableau or free cells
            // Check if there are any valid moves for this card

            // Check free cells
            for (let i = 0; i < 4; i++) {
                if (this.freeCells[i] === null) {
                    return true;
                }
            }

            // Check tableau columns
            for (let targetCol = 0; targetCol < 8; targetCol++) {
                if (this.canMoveToTableau(card, targetCol)) {
                    return true;
                }
            }

            return false; // No valid moves found for this foundation card
        }

        if (source === 'tableau') {
            const column = this.tableau[columnIndex];
            if (column.length === 0) return false;

            // Find the card's position in the column
            const cardPosition = column.findIndex(c => c.id === card.id);
            if (cardPosition === -1) return false;

            // Only the bottom card or cards that form a valid sequence to the bottom can be moved
            const sequence = this.getMaxMovableSequence(columnIndex);
            const cardInSequence = sequence.findIndex(c => c.id === card.id);

            if (cardInSequence === -1) {
                return false; // Card is not part of movable sequence from bottom
            }

            // Cards that are part of a valid sequence should be movable
            // Let the user try to move them and get feedback if space is insufficient
            return true;
        }

        return false;
    }

    // Render free cells
    renderFreeCells() {
        for (let i = 0; i < 4; i++) {
            const $pile = $(`#freecell-${i}`);
            $pile.empty();
            
            if (this.freeCells[i]) {
                const $card = this.createCardElement(this.freeCells[i]);
                $pile.append($card);
            } else {
                $pile.append('<div class="pile-placeholder">Free</div>');
            }
        }
    }

    // Render foundation piles
    renderFoundations() {
        for (const suit of this.suits) {
            const $pile = $(`#foundation-${suit}`);
            $pile.empty();
            
            const cards = this.foundations[suit];
            if (cards.length > 0) {
                const topCard = cards[cards.length - 1];
                const $card = this.createCardElement(topCard);
                $pile.append($card);
            } else {
                $pile.append(`<div class="pile-placeholder">${this.suitSymbols[suit]}</div>`);
            }
        }
    }

    // Render tableau columns
    renderTableau() {
        for (let col = 0; col < 8; col++) {
            const $pile = $(`#tableau-${col}`);
            $pile.empty();
            
            const cards = this.tableau[col];
            cards.forEach((card, index) => {
                const $card = this.createCardElement(card);
                $card.css({
                    top: `${index * 25}px`,
                    left: '16px',
                    zIndex: index + 1
                });
                $pile.append($card);
            });
        }
    }

    // Create a card DOM element
    createCardElement(card) {
        const $card = $('<div>')
            .addClass('card draggable-card')
            .addClass(card.color)
            .attr('data-card-id', card.id)
            .attr('data-suit', card.suit)
            .attr('data-rank', card.rank)
            .attr('data-value', card.value);

        const $top = $('<div>').addClass('card-top');
        $top.append(`<span>${card.rank}</span>`);
        $top.append(`<span>${this.suitSymbols[card.suit]}</span>`);

        const $center = $('<div>').addClass('card-center');
        $center.text(this.suitSymbols[card.suit]);

        const $bottom = $('<div>').addClass('card-bottom');
        $bottom.append(`<span>${card.rank}</span>`);
        $bottom.append(`<span>${this.suitSymbols[card.suit]}</span>`);

        $card.append($top, $center, $bottom);
        return $card;
    }

    // Start the game timer
    startTimer() {
        if (this.timer) {
            clearInterval(this.timer);
        }
        
        this.timer = setInterval(() => {
            if (!this.gameWon) {
                const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                $('#timer').text(`${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
            }
        }, 1000);
    }

    // Update game statistics
    updateStats() {
        $('#score').text(this.score);
        $('#moves').text(this.moves);
    }

    // Check if a card can be moved to a foundation pile
    canMoveToFoundation(card, suit) {
        if (card.suit !== suit) return false;
        
        const foundation = this.foundations[suit];
        if (foundation.length === 0) {
            return card.rank === 'A';
        }
        
        const topCard = foundation[foundation.length - 1];
        return card.value === topCard.value + 1;
    }

    // Check if a card can be moved to a tableau column
    canMoveToTableau(card, columnIndex) {
        const column = this.tableau[columnIndex];
        if (column.length === 0) {
            return true; // Any card can go on empty column
        }

        const topCard = column[column.length - 1];
        return card.color !== topCard.color && card.value === topCard.value - 1;
    }

    // Check if a sequence of cards can be moved together
    canMoveSequence(cards, targetColumn) {
        if (cards.length === 0) return false;

        // Check if the sequence is valid (alternating colors, descending values)
        for (let i = 0; i < cards.length - 1; i++) {
            const current = cards[i];
            const next = cards[i + 1];
            if (current.color === next.color || current.value !== next.value + 1) {
                return false;
            }
        }

        // Check if we have enough free cells and empty columns to move this sequence
        const freeCells = this.freeCells.filter(cell => cell === null).length;
        const emptyColumns = this.tableau.filter(col => col.length === 0).length;

        // If target is empty, we need one less empty column
        const availableEmptyColumns = this.tableau[targetColumn].length === 0 ?
            Math.max(0, emptyColumns - 1) : emptyColumns;

        // Ensure we don't have negative values in the calculation
        const maxMovableCards = Math.max(1, (freeCells + 1) * Math.pow(2, Math.max(0, availableEmptyColumns)));

        return cards.length <= maxMovableCards;
    }

    // Get the maximum number of cards that can be moved as a sequence
    getMaxMovableSequence(columnIndex) {
        const column = this.tableau[columnIndex];
        if (column.length === 0) return [];

        const sequence = [column[column.length - 1]];

        for (let i = column.length - 2; i >= 0; i--) {
            const current = column[i];
            const last = sequence[sequence.length - 1];

            // In FreeCell, sequences are built down by alternating colors
            // current card should be one value higher than the last card in sequence
            // and different color
            if (current.color !== last.color && current.value === last.value + 1) {
                sequence.push(current);
            } else {
                break;
            }
        }

        const result = sequence.reverse();
        return result;
    }

    // Validate if a sequence of cards is actually movable (alternating colors, descending values)
    isValidMovableSequence(cards) {
        if (cards.length <= 1) return true;

        for (let i = 0; i < cards.length - 1; i++) {
            const current = cards[i];
            const next = cards[i + 1];

            // Check if colors alternate and values descend by 1
            if (current.color === next.color || current.value !== next.value + 1) {
                return false;
            }
        }

        return true;
    }

    // Bind event handlers
    bindEvents() {
        $(document).ready(() => {
            this.initializeGame();
            this.setupEventHandlers();
        });
    }

    setupEventHandlers() {
        // Button events
        $('#newGameBtn').on('click', () => this.initializeGame());
        $('#undoBtn').on('click', () => this.undoMove());
        $('#hintBtn').on('click', () => this.showHint());
        $('#helpBtn').on('click', () => this.showHelp());
        $('#homeBtn').on('click', () => window.location.href = '/');
        $('#fullscreenBtn').on('click', () => this.toggleFullscreen());
        
        // Game message events
        $('#playAgainBtn').on('click', () => {
            this.hideGameMessage();
            this.initializeGame();
        });
        $('#closeMessageBtn').on('click', () => this.hideGameMessage());
        
        // Help panel events
        $('#closeHelpBtn, #closeHelpBtnBottom').on('click', () => this.hideHelp());
        
        // Card drag and drop events
        this.setupDragAndDrop();
        
        // Single-click for smart auto-move
        $(document).on('click', '.card', (e) => this.handleCardClick(e));
    }

    // Setup drag and drop functionality
    setupDragAndDrop() {
        let startX, startY, hasMoved = false, dragStarted = false;

        $(document).on('mousedown touchstart', '.card', (e) => {
            if (this.isAnimatingCard || this.isProcessingAction) return;

            e.preventDefault();
            const touch = e.type === 'touchstart' ? e.originalEvent.touches[0] : e;
            startX = touch.clientX;
            startY = touch.clientY;
            hasMoved = false;
            dragStarted = false;

            this.startDrag(e);
        });

        $(document).on('mousemove touchmove', (e) => {
            if (!this.draggedCards || this.draggedCards.length === 0) return;

            e.preventDefault();
            const touch = e.type === 'touchmove' ? e.originalEvent.touches[0] : e;

            if (!hasMoved) {
                const deltaX = Math.abs(touch.clientX - startX);
                const deltaY = Math.abs(touch.clientY - startY);
                if (deltaX > this.dragThreshold || deltaY > this.dragThreshold) {
                    hasMoved = true;
                    if (!dragStarted) {
                        this.startActualDrag();
                        dragStarted = true;
                    }
                }
            }

            if (hasMoved && this.isDragging) {
                this.updateDrag(e);
            }
        });

        $(document).on('mouseup touchend', (e) => {
            if (this.draggedCards && this.draggedCards.length > 0) {
                e.preventDefault();
                if (hasMoved && this.isDragging) {
                    this.endDrag(e);
                } else {
                    // Just a click, not a drag
                    this.cancelDrag();
                }
            }
        });
    }

    // Start dragging a card or sequence
    startDrag(e) {
        const $card = $(e.target).closest('.card');
        if (!$card.length) return;

        // Check if this card is disabled
        if ($card.hasClass('card-disabled')) {
            e.preventDefault();
            e.stopPropagation();
            return;
        }

        const cardData = this.getCardData($card);
        const sourceInfo = this.getCardSource($card);

        if (!sourceInfo) return;

        // Determine what cards to drag
        let cardsToDrag = [cardData];

        if (sourceInfo.type === 'tableau') {
            const sequence = this.getMaxMovableSequence(sourceInfo.index);
            const cardIndex = sequence.findIndex(c => c.id === cardData.id);
            if (cardIndex >= 0) {
                cardsToDrag = sequence.slice(cardIndex);

                // Additional validation: ensure the cards we're about to drag form a valid sequence
                if (cardsToDrag.length > 1) {
                    const isValidSequence = this.isValidMovableSequence(cardsToDrag);
                    if (!isValidSequence) {
                        cardsToDrag = [cardData];
                    }
                }
            }
        } else if (sourceInfo.type === 'foundation') {
            // Foundation cards can only be moved individually
            cardsToDrag = [cardData];
        }

        this.draggedCards = cardsToDrag;
        this.draggedFrom = sourceInfo;

        // Record original positions like spider solitaire
        this.recordOriginalCardPositions($card, cardsToDrag, sourceInfo);

        const touch = e.type === 'touchstart' ? e.originalEvent.touches[0] : e;
        this.dragOffset = {
            x: touch.clientX - $card[0].getBoundingClientRect().left,
            y: touch.clientY - $card[0].getBoundingClientRect().top
        };
    }

    // Record original card positions (like spider solitaire)
    recordOriginalCardPositions($clickedCard, cardsToDrag, sourceInfo) {
        this.originalCardPositions = [];

        if (sourceInfo.type === 'tableau') {
            const $pile = $(`#tableau-${sourceInfo.index}`);
            const $cards = $pile.find('.card');
            const totalCards = $cards.length;
            const cardsToRecord = cardsToDrag.length;

            // Record positions of the cards being dragged
            for (let i = totalCards - cardsToRecord; i < totalCards; i++) {
                const $card = $cards.eq(i);
                const rect = $card[0].getBoundingClientRect();
                this.originalCardPositions.push({
                    element: $card,
                    left: rect.left,
                    top: rect.top,
                    position: $card.css('position'),
                    zIndex: $card.css('z-index')
                });
            }
        } else {
            // For freecell and foundation, just record the single card
            const rect = $clickedCard[0].getBoundingClientRect();
            this.originalCardPositions.push({
                element: $clickedCard,
                left: rect.left,
                top: rect.top,
                position: $clickedCard.css('position'),
                zIndex: $clickedCard.css('z-index')
            });
        }
    }

    // Start actual dragging (like spider solitaire)
    startActualDrag() {
        console.log('🚀 startActualDrag called');
        console.log('originalCardPositions:', this.originalCardPositions);

        this.isDragging = true;
        this.draggedElements = [];

        this.originalCardPositions.forEach((pos, index) => {
            const $card = pos.element;
            const rect = $card[0].getBoundingClientRect();

            console.log(`📍 Card ${index} starting drag from:`, {
                left: rect.left,
                top: rect.top
            });

            $card.css({
                position: 'fixed',
                left: rect.left + 'px',
                top: rect.top + 'px',
                zIndex: index + 9999,
                pointerEvents: 'none',
                transition: 'none',
                transform: 'none'
            });
            this.draggedElements.push($card);
        });

        console.log('✅ Drag elements created:', this.draggedElements.length);
    }

    // Update drag position
    updateDrag(e) {
        if (!this.isDragging || !this.draggedElements) return;

        const touch = e.type === 'touchmove' ? e.originalEvent.touches[0] : e;
        this.updateDragPosition(touch.clientX, touch.clientY);
    }

    // Update the position of dragged elements
    updateDragPosition(x, y) {
        if (!this.draggedElements) return;

        this.draggedElements.forEach(($element, index) => {
            $element.css({
                left: (x - this.dragOffset.x) + 'px',
                top: (y - this.dragOffset.y + index * 25) + 'px'
            });
        });
    }



    // Check if the current drag is a valid drop
    isValidDrop($target) {
        if (!this.draggedCards || this.draggedCards.length === 0) return false;

        const firstCard = this.draggedCards[0];

        if ($target.hasClass('freecell-pile')) {
            // Can only move single cards to free cells
            if (this.draggedCards.length > 1) return false;
            const index = parseInt($target.attr('id').split('-')[1]);
            return this.freeCells[index] === null;
        }

        if ($target.hasClass('foundation-pile')) {
            // Can only move single cards to foundations
            if (this.draggedCards.length > 1) return false;

            // Don't allow foundation cards to be moved back to foundations
            if (this.draggedFrom && this.draggedFrom.type === 'foundation') {
                return false;
            }

            const suit = $target.attr('data-suit');
            return this.canMoveToFoundation(firstCard, suit);
        }

        if ($target.hasClass('tableau-pile')) {
            const index = parseInt($target.attr('id').split('-')[1]);
            // For tableau moves, use canMoveSequenceToTableau which handles both single cards and sequences
            return this.canMoveSequenceToTableau(this.draggedCards, index);
        }

        return false;
    }

    // Cancel drag operation
    cancelDrag() {
        this.draggedCards = null;
        this.draggedFrom = null;
        this.originalCardPositions = [];
        this.isDragging = false;
    }

    // End the drag operation
    async endDrag(e) {
        console.log('🏁 endDrag called');
        if (!this.isDragging) {
            console.log('❌ Not dragging, returning');
            return;
        }

        const touch = e.type === 'touchend' ? e.originalEvent.changedTouches[0] : e;
        const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
        const $target = $(elementBelow).closest('.freecell-pile, .foundation-pile, .tableau-pile');

        console.log('🎯 Drop target:', $target.length ? $target.attr('id') : 'none');

        let moveSuccessful = false;

        if ($target.length && this.isValidDrop($target)) {
            console.log('✅ Valid drop, performing move');
            moveSuccessful = this.performMove($target);
        } else {
            console.log('❌ Invalid drop or no target');
        }

        if (moveSuccessful) {
            console.log('✅ Move successful, cleaning up');
            // Clean up and re-render for successful moves
            this.cleanupSuccessfulDrag();
            this.renderGame();
            this.updateStats();
            this.updateCardStates();
            this.checkForWin();
            this.tryAutoComplete();

            // Check for auto-movable cards to foundation
            setTimeout(() => {
                this.autoMoveToFoundations();
            }, 100);
        } else {
            console.log('❌ Move failed, returning cards to original position');
            // Return cards to original position for failed moves
            await this.returnCardsToOriginalPosition();
        }
    }

    // Clean up after successful drag
    cleanupSuccessfulDrag() {
        if (this.draggedElements) {
            this.draggedElements.forEach($element => {
                $element.css({
                    position: '',
                    left: '',
                    top: '',
                    zIndex: '',
                    pointerEvents: '',
                    transition: '',
                    transform: ''
                });
            });
        }
        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElements = [];
        this.originalCardPositions = [];
        this.isDragging = false;
    }

    // Animate single card movement (simplified for foundation moves)
    async animateSingleCard($card, startPos, endPos, duration = 300) {
        return new Promise((resolve) => {
            if (!$card.length) {
                resolve();
                return;
            }

            // Create a clone for animation to avoid interfering with the original card
            const $clone = $card.clone();

            // Ensure the clone has the same visual appearance
            $clone.css({
                position: 'fixed',
                left: startPos.left + 'px',
                top: startPos.top + 'px',
                width: $card.outerWidth() + 'px',
                height: $card.outerHeight() + 'px',
                zIndex: 10000,
                transition: 'none',
                transform: 'translateZ(0)',
                pointerEvents: 'none',
                opacity: '1',
                visibility: 'visible'
            });

            // Hide original card during animation
            const originalOpacity = $card.css('opacity');
            $card.css('opacity', '0');

            // Add clone to body
            $('body').append($clone);

            // Force reflow
            $clone[0].offsetHeight;

            // Animate to end position with smooth easing
            $clone.css({
                left: endPos.left + 'px',
                top: endPos.top + 'px',
                transition: `all ${duration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`
            });

            setTimeout(() => {
                // Remove the clone and restore original card
                $clone.remove();
                $card.css('opacity', originalOpacity);
                resolve();
            }, duration);
        });
    }

    // Return cards to original position using professional animation
    async returnCardsToOriginalPosition() {
        console.log('🔄 returnCardsToOriginalPosition called');
        console.log('draggedElements:', this.draggedElements);
        console.log('originalCardPositions:', this.originalCardPositions);

        if (!Array.isArray(this.draggedElements) || this.draggedElements.length === 0) {
            console.log('❌ No dragged elements to return');
            return;
        }

        // Animate each card back to its original position
        const animations = this.draggedElements.map((element, index) => {
            const originalPos = this.originalCardPositions[index];
            const currentRect = element[0].getBoundingClientRect();

            const startPos = {
                left: currentRect.left,
                top: currentRect.top
            };

            const endPos = {
                left: originalPos.left,
                top: originalPos.top
            };

            // Calculate distance to determine animation duration
            const distance = Math.sqrt(
                Math.pow(endPos.left - startPos.left, 2) +
                Math.pow(endPos.top - startPos.top, 2)
            );

            // Set minimum duration for short distances to avoid jitter
            const baseDuration = this.isMobile() ? 200 : 300;
            const minDuration = 150;
            const maxDuration = 500;

            // Scale duration based on distance, with minimum threshold
            let duration = Math.max(minDuration, Math.min(maxDuration, distance * 0.8));
            if (distance < 50) {
                // For very short distances, use a fixed smooth duration
                duration = baseDuration;
            }

            console.log(`📍 Card ${index} distance: ${distance.toFixed(1)}px, duration: ${duration}ms`);
            console.log(`📍 Card ${index} animating from:`, startPos, 'to:', endPos);

            return this.animateSingleCard(element, startPos, endPos, duration);
        });

        // Wait for all animations to complete
        await Promise.all(animations);

        // Clean up
        this.draggedCards = null;
        this.draggedFrom = null;
        this.draggedElements = [];
        this.originalCardPositions = [];
        this.isDragging = false;

        // Re-render the game to ensure cards are properly positioned
        this.renderGame();
        this.updateCardStates();
        console.log('✅ All animations complete, cleanup done');
    }

    // Perform the actual move
    performMove($target) {
        if (!this.draggedCards || !this.draggedFrom) return false;

        const firstCard = this.draggedCards[0];

        // Reset consecutive auto moves counter on manual move
        this.consecutiveAutoMoves = 0;

        // If this is not a move from foundation, clear the recently moved set
        if (!this.draggedFrom || this.draggedFrom.type !== 'foundation') {
            this.recentlyMovedFromFoundation.clear();
        }

        // Remove cards from source
        this.removeCardsFromSource();

        // Add cards to target
        if ($target.hasClass('freecell-pile')) {
            const index = parseInt($target.attr('id').split('-')[1]);
            this.freeCells[index] = firstCard;
        } else if ($target.hasClass('foundation-pile')) {
            const suit = $target.attr('data-suit');
            this.foundations[suit].push(firstCard);
            this.score += 10; // Points for moving to foundation
        } else if ($target.hasClass('tableau-pile')) {
            const index = parseInt($target.attr('id').split('-')[1]);
            this.tableau[index].push(...this.draggedCards);
        }

        // Record move for undo
        this.recordMove();
        this.moves++;

        // Scoring: moving from foundation costs points, other moves gain points
        if (this.draggedFrom && this.draggedFrom.type === 'foundation') {
            this.score -= 15; // Penalty for moving from foundation
            // Mark this card as recently moved from foundation to prevent immediate auto-move back
            this.recentlyMovedFromFoundation.add(firstCard.id);
        } else {
            this.score += 1; // Point for any other move
        }

        return true;
    }

    // Remove cards from their source location
    removeCardsFromSource() {
        if (!this.draggedFrom || !this.draggedCards) return;

        if (this.draggedFrom.type === 'freecell') {
            this.freeCells[this.draggedFrom.index] = null;
        } else if (this.draggedFrom.type === 'foundation') {
            const suit = this.draggedFrom.suit;
            this.foundations[suit].pop();
        } else if (this.draggedFrom.type === 'tableau') {
            const column = this.tableau[this.draggedFrom.index];
            column.splice(column.length - this.draggedCards.length);
        }
    }

    // Get card data from DOM element
    getCardData($card) {
        return {
            id: $card.attr('data-card-id'),
            suit: $card.attr('data-suit'),
            rank: $card.attr('data-rank'),
            value: parseInt($card.attr('data-value')),
            color: this.suitColors[$card.attr('data-suit')]
        };
    }

    // Get the source location of a card
    getCardSource($card) {
        const $pile = $card.closest('.freecell-pile, .foundation-pile, .tableau-pile');

        if ($pile.hasClass('freecell-pile')) {
            const index = parseInt($pile.attr('id').split('-')[1]);
            return { type: 'freecell', index: index };
        }

        if ($pile.hasClass('foundation-pile')) {
            const suit = $pile.attr('data-suit');
            return { type: 'foundation', suit: suit };
        }

        if ($pile.hasClass('tableau-pile')) {
            const index = parseInt($pile.attr('id').split('-')[1]);
            return { type: 'tableau', index: index };
        }

        return null;
    }

    // Handle single-click for smart auto-move
    handleCardClick(e) {
        // Prevent if we're in the middle of dragging or other operations
        if (this.isDragging || this.isAnimatingCard || this.isProcessingAction) return;

        // Check if this card is disabled
        const $card = $(e.target).closest('.card');
        if ($card.hasClass('card-disabled')) {
            e.preventDefault();
            e.stopPropagation();
            return;
        }

        // Add a small delay to avoid conflict with drag start
        setTimeout(() => {
            if (!this.isDragging && !this.isAnimatingCard) {
                this.performSmartAutoMove(e);
            }
        }, 50);
    }

    // Perform smart auto-move on single click
    performSmartAutoMove(e) {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        const $card = $(e.target).closest('.card');
        if (!$card.length) return;

        const cardData = this.getCardData($card);
        const sourceInfo = this.getCardSource($card);

        if (!sourceInfo) return;

        // Check if this card is actually movable
        if (sourceInfo.type === 'tableau') {
            if (!this.isCardMovable(cardData, sourceInfo.index, -1, 'tableau')) {
                return; // Card is not movable
            }
        } else if (sourceInfo.type === 'foundation') {
            if (!this.isCardMovable(cardData, -1, -1, 'foundation', sourceInfo.suit)) {
                return; // Card is not movable
            }
        }

        // Determine what cards to move
        let cardsToMove = [cardData];

        if (sourceInfo.type === 'tableau') {
            // For tableau, get the sequence starting from clicked card
            const sequence = this.getMaxMovableSequence(sourceInfo.index);
            const cardIndex = sequence.findIndex(c => c.id === cardData.id);
            if (cardIndex >= 0) {
                cardsToMove = sequence.slice(cardIndex);

                // Additional validation: ensure the cards we're about to move form a valid sequence
                if (cardsToMove.length > 1) {
                    const isValidSequence = this.isValidMovableSequence(cardsToMove);
                    if (!isValidSequence) {
                        cardsToMove = [cardData];
                    }
                }
            } else {
                return; // Card is not part of movable sequence
            }
        } else if (sourceInfo.type === 'foundation') {
            // Foundation cards can only be moved individually
            cardsToMove = [cardData];
        }

        // For single card moves, try foundation first
        if (cardsToMove.length === 1) {
            // Priority 1: Try to move to foundation
            for (const suit of this.suits) {
                if (this.canMoveToFoundation(cardData, suit)) {
                    this.performAnimatedAutoMove(cardData, sourceInfo, { type: 'foundation', suit: suit });
                    return;
                }
            }

            // Priority 2: Try to move to an empty free cell (only if no better tableau move)
            let bestTableauMove = null;
            for (let i = 0; i < 8; i++) {
                if (this.canMoveToTableau(cardData, i)) {
                    bestTableauMove = { type: 'tableau', index: i };
                    break;
                }
            }

            // If no tableau move available, try free cell
            if (!bestTableauMove) {
                for (let i = 0; i < 4; i++) {
                    if (this.freeCells[i] === null) {
                        this.performAnimatedAutoMove(cardData, sourceInfo, { type: 'freecell', index: i });
                        return;
                    }
                }
            } else {
                // Perform the tableau move
                this.performAnimatedAutoMove(cardData, sourceInfo, bestTableauMove);
            }
        } else {
            // For multiple cards, only try tableau moves
            for (let i = 0; i < 8; i++) {
                if (sourceInfo.type === 'tableau' && i === sourceInfo.index) continue;

                if (this.canMoveSequenceToTableau(cardsToMove, i)) {
                    this.performAnimatedSequenceMove(cardsToMove, sourceInfo, { type: 'tableau', index: i });
                    return;
                }
            }
        }
    }

    // Check if a card is the top card of its pile
    isTopCard($card, sourceInfo) {
        if (sourceInfo.type === 'freecell' || sourceInfo.type === 'foundation') {
            return true; // Free cells and foundations only have one card
        }

        if (sourceInfo.type === 'tableau') {
            const column = this.tableau[sourceInfo.index];
            if (column.length === 0) return false;

            const cardData = this.getCardData($card);
            const topCard = column[column.length - 1];
            return cardData.id === topCard.id;
        }

        return false;
    }

    // Check if a sequence can be moved to a tableau column
    canMoveSequenceToTableau(cards, targetColumnIndex) {
        if (!cards || cards.length === 0) return false;

        const targetColumn = this.tableau[targetColumnIndex];
        const firstCard = cards[0];

        // Check if the first card can be placed on the target column
        if (targetColumn.length === 0) {
            // Any card can go on empty column
            return true;
        }

        const topCard = targetColumn[targetColumn.length - 1];
        if (firstCard.color === topCard.color || firstCard.value !== topCard.value - 1) {
            return false;
        }

        // Check if we have enough free cells and empty columns to move this sequence
        const freeCells = this.freeCells.filter(cell => cell === null).length;
        const emptyColumns = this.tableau.filter(col => col.length === 0).length;

        // If target is empty, we need one less empty column
        const availableEmptyColumns = targetColumn.length === 0 ?
            Math.max(0, emptyColumns - 1) : emptyColumns;

        // Ensure we don't have negative values in the calculation
        const maxMovableCards = Math.max(1, (freeCells + 1) * Math.pow(2, Math.max(0, availableEmptyColumns)));

        return cards.length <= maxMovableCards;
    }

    // Perform animated sequence move
    async performAnimatedSequenceMove(cards, sourceInfo, targetInfo) {
        if (sourceInfo.type !== 'tableau' || targetInfo.type !== 'tableau') return;

        this.isAnimatingCard = true;

        // Get source cards elements
        const $sourcePile = $(`#tableau-${sourceInfo.index}`);
        const $sourceCards = $sourcePile.find('.card').slice(-cards.length);

        // Get target position
        const $target = $(`#tableau-${targetInfo.index}`);
        const targetRect = $target[0].getBoundingClientRect();

        // Calculate positions for each card
        const animations = [];
        const cardSpacing = 25;
        const targetColumn = this.tableau[targetInfo.index];

        for (let i = 0; i < cards.length; i++) {
            const $card = $sourceCards.eq(i);
            if (!$card.length) continue;

            const sourceRect = $card[0].getBoundingClientRect();
            const startPos = {
                left: sourceRect.left,
                top: sourceRect.top
            };

            const endPos = {
                left: targetRect.left + (targetRect.width - sourceRect.width) / 2,
                top: targetRect.top + (targetColumn.length + i) * cardSpacing
            };

            animations.push(this.animateSingleCard($card, startPos, endPos, 300));
        }

        // Perform the move in data
        this.tableau[sourceInfo.index].splice(-cards.length);
        this.tableau[targetInfo.index].push(...cards);

        // Record move for undo
        this.recordMove();
        this.moves++;
        this.score += 1;

        // Wait for animations to complete
        await Promise.all(animations);

        // Update display
        this.renderGame();
        this.updateStats();
        this.updateCardStates();
        this.isAnimatingCard = false;

        // Check for auto-movable cards to foundation
        setTimeout(() => {
            this.autoMoveToFoundations();
        }, 100);
    }

    // Perform an animated automatic move
    async performAnimatedAutoMove(cardData, sourceInfo, targetInfo) {
        this.isAnimatingCard = true;

        // Get source card element
        let $sourceCard;
        if (sourceInfo.type === 'tableau') {
            const $pile = $(`#tableau-${sourceInfo.index}`);
            $sourceCard = $pile.find('.card').last();
        } else if (sourceInfo.type === 'freecell') {
            $sourceCard = $(`#freecell-${sourceInfo.index} .card`);
        } else if (sourceInfo.type === 'foundation') {
            $sourceCard = $(`#foundation-${sourceInfo.suit} .card`).last();
        }

        if (!$sourceCard || !$sourceCard.length) {
            this.isAnimatingCard = false;
            return;
        }

        // Get target position
        let $target;
        if (targetInfo.type === 'foundation') {
            $target = $(`#foundation-${targetInfo.suit}`);
        } else if (targetInfo.type === 'freecell') {
            $target = $(`#freecell-${targetInfo.index}`);
        } else if (targetInfo.type === 'tableau') {
            $target = $(`#tableau-${targetInfo.index}`);
        }

        if (!$target || !$target.length) {
            this.isAnimatingCard = false;
            return;
        }

        const sourceRect = $sourceCard[0].getBoundingClientRect();
        const targetRect = $target[0].getBoundingClientRect();

        const startPos = {
            left: sourceRect.left,
            top: sourceRect.top
        };

        // Calculate end position based on target type and existing cards
        let endPos = {
            left: targetRect.left + (targetRect.width - sourceRect.width) / 2,
            top: targetRect.top
        };

        if (targetInfo.type === 'foundation') {
            // For foundation, stack cards with small offset
            const foundation = this.foundations[targetInfo.suit];
            const cardOffset = foundation.length * 2;
            endPos.top += cardOffset;
        } else if (targetInfo.type === 'tableau') {
            // For tableau, position at the bottom of existing cards
            const column = this.tableau[targetInfo.index];
            if (column.length > 0) {
                const cardSpacing = 25; // Spacing between cards in tableau
                endPos.top += column.length * cardSpacing;
            }
        } else if (targetInfo.type === 'freecell') {
            // For free cells, center vertically
            endPos.top += (targetRect.height - sourceRect.height) / 2;
        }

        // Perform the move in data
        this.performDataMove(cardData, sourceInfo, targetInfo);

        // Animate the card
        await this.animateSingleCard($sourceCard, startPos, endPos, 300);

        // Update display
        this.renderGame();
        this.updateStats();
        this.updateCardStates();
        this.isAnimatingCard = false;

        // Check for auto-movable cards to foundation
        setTimeout(() => {
            this.autoMoveToFoundations();
        }, 100);
    }

    // Perform data move without animation
    performDataMove(cardData, sourceInfo, targetInfo) {
        // Reset consecutive auto moves counter on manual move
        this.consecutiveAutoMoves = 0;

        // Remove from source
        if (sourceInfo.type === 'freecell') {
            this.freeCells[sourceInfo.index] = null;
        } else if (sourceInfo.type === 'foundation') {
            this.foundations[sourceInfo.suit].pop();
        } else if (sourceInfo.type === 'tableau') {
            this.tableau[sourceInfo.index].pop();
        }

        // Add to target
        if (targetInfo.type === 'freecell') {
            this.freeCells[targetInfo.index] = cardData;
        } else if (targetInfo.type === 'foundation') {
            this.foundations[targetInfo.suit].push(cardData);
            this.score += 10;
        } else if (targetInfo.type === 'tableau') {
            this.tableau[targetInfo.index].push(cardData);
        }

        // Record move for undo
        this.recordMove();
        this.moves++;
        this.score += 1;
    }

    // Perform an automatic move
    performAutoMove(cardData, sourceInfo, targetInfo) {
        // Remove from source
        if (sourceInfo.type === 'freecell') {
            this.freeCells[sourceInfo.index] = null;
        } else if (sourceInfo.type === 'foundation') {
            this.foundations[sourceInfo.suit].pop();
        } else if (sourceInfo.type === 'tableau') {
            this.tableau[sourceInfo.index].pop();
        }

        // Add to target
        if (targetInfo.type === 'freecell') {
            this.freeCells[targetInfo.index] = cardData;
        } else if (targetInfo.type === 'foundation') {
            this.foundations[targetInfo.suit].push(cardData);
            this.score += 10;
        } else if (targetInfo.type === 'tableau') {
            this.tableau[targetInfo.index].push(cardData);
        }

        this.recordMove();
        this.moves++;
        this.score += 1;

        this.renderGame();
        this.updateStats();
        this.updateCardStates();
        this.checkForWin();
        this.tryAutoComplete();

        // Check for auto-movable cards to foundation
        setTimeout(() => {
            this.autoMoveToFoundations();
        }, 100);
    }

    // Record a move for undo functionality
    recordMove() {
        const gameState = {
            freeCells: [...this.freeCells],
            foundations: {
                hearts: [...this.foundations.hearts],
                diamonds: [...this.foundations.diamonds],
                clubs: [...this.foundations.clubs],
                spades: [...this.foundations.spades]
            },
            tableau: this.tableau.map(col => [...col]),
            score: this.score,
            moves: this.moves
        };

        this.moveHistory.push(gameState);

        // Limit history to prevent memory issues
        if (this.moveHistory.length > 100) {
            this.moveHistory.shift();
        }
    }

    // Undo the last move
    undoMove() {
        if (this.moveHistory.length === 0 || this.isAnimatingCard || this.isProcessingAction) return;

        // Reset consecutive auto moves counter on undo
        this.consecutiveAutoMoves = 0;
        // Clear recently moved from foundation set on undo
        this.recentlyMovedFromFoundation.clear();

        const previousState = this.moveHistory.pop();

        this.freeCells = previousState.freeCells;
        this.foundations = previousState.foundations;
        this.tableau = previousState.tableau;
        this.score = previousState.score;
        this.moves = previousState.moves;

        this.renderGame();
        this.updateStats();
        this.updateCardStates();
    }

    // Try to automatically complete obvious moves
    tryAutoComplete() {
        if (this.isAutoCompleting) return;

        this.isAutoCompleting = true;
        let madeMove = false;

        // Try to move low cards to foundations
        for (const suit of this.suits) {
            const foundation = this.foundations[suit];
            const nextValue = foundation.length + 1;

            // Only auto-complete if the card is safe to move (low value)
            if (nextValue <= 3) {
                // Check tableau columns
                for (let col = 0; col < 8; col++) {
                    const column = this.tableau[col];
                    if (column.length > 0) {
                        const topCard = column[column.length - 1];
                        if (topCard.suit === suit && topCard.value === nextValue) {
                            this.performAutoMove(topCard,
                                { type: 'tableau', index: col },
                                { type: 'foundation', suit: suit }
                            );
                            madeMove = true;
                            break;
                        }
                    }
                }

                // Check free cells
                if (!madeMove) {
                    for (let i = 0; i < 4; i++) {
                        const card = this.freeCells[i];
                        if (card && card.suit === suit && card.value === nextValue) {
                            this.performAutoMove(card,
                                { type: 'freecell', index: i },
                                { type: 'foundation', suit: suit }
                            );
                            madeMove = true;
                            break;
                        }
                    }
                }
            }

            if (madeMove) break;
        }

        this.isAutoCompleting = false;

        // If we made a move, try again after a short delay
        if (madeMove) {
            setTimeout(() => this.tryAutoComplete(), 500);
        }
    }

    // Check if the game is won
    checkForWin() {
        const totalFoundationCards = Object.values(this.foundations)
            .reduce((sum, pile) => sum + pile.length, 0);

        if (totalFoundationCards === 52) {
            this.gameWon = true;
            this.score += 500; // Bonus for winning
            clearInterval(this.timer);
            this.showWinMessage();
        }
    }

    // Show win message
    showWinMessage() {
        const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // Add celebration animation to all cards
        $('.card').addClass('celebration');

        // Show win message after a short delay
        setTimeout(() => {
            $('#messageTitle').text('🎉 Congratulations! 🎉');
            $('#messageText').text('You successfully completed FreeCell!');
            $('#finalScore').text(this.score);
            $('#finalTime').text(timeStr);
            $('#finalMoves').text(this.moves);
            $('#gameMessage').removeClass('hidden');

            // Remove celebration animation after showing message
            setTimeout(() => {
                $('.card').removeClass('celebration');
            }, 2000);
        }, 500);
    }

    // Hide game message
    hideGameMessage() {
        $('#gameMessage').addClass('hidden');
    }

    // Show hint
    showHint() {
        if (this.isAnimatingCard || this.isProcessingAction) return;

        // Clear previous hints
        $('.card-hint, .pile-hint').removeClass('card-hint pile-hint');

        // Look for possible moves
        let hintFound = false;

        // Check for moves to foundation
        for (const suit of this.suits) {
            if (hintFound) break;

            // Check tableau
            for (let col = 0; col < 8; col++) {
                const column = this.tableau[col];
                if (column.length > 0) {
                    const topCard = column[column.length - 1];
                    if (this.canMoveToFoundation(topCard, suit)) {
                        $(`#tableau-${col} .card:last`).addClass('card-hint');
                        $(`#foundation-${suit}`).addClass('pile-hint');
                        hintFound = true;
                        break;
                    }
                }
            }

            // Check free cells
            if (!hintFound) {
                for (let i = 0; i < 4; i++) {
                    const card = this.freeCells[i];
                    if (card && this.canMoveToFoundation(card, suit)) {
                        $(`#freecell-${i} .card`).addClass('card-hint');
                        $(`#foundation-${suit}`).addClass('pile-hint');
                        hintFound = true;
                        break;
                    }
                }
            }
        }

        // If no foundation moves, look for tableau moves
        if (!hintFound) {
            for (let sourceCol = 0; sourceCol < 8; sourceCol++) {
                if (hintFound) break;

                const sourceColumn = this.tableau[sourceCol];
                if (sourceColumn.length === 0) continue;

                const sequence = this.getMaxMovableSequence(sourceCol);
                if (sequence.length === 0) continue;

                for (let targetCol = 0; targetCol < 8; targetCol++) {
                    if (sourceCol === targetCol) continue;

                    if (this.canMoveSequenceToTableau(sequence, targetCol)) {
                        $(`#tableau-${sourceCol} .card:last`).addClass('card-hint');
                        $(`#tableau-${targetCol}`).addClass('pile-hint');
                        hintFound = true;
                        break;
                    }
                }
            }
        }

        // Clear hints after 3 seconds
        setTimeout(() => {
            $('.card-hint, .pile-hint').removeClass('card-hint pile-hint');
        }, 3000);
    }

    // Show help panel
    showHelp() {
        $('#helpPanel').removeClass('hidden');
    }

    // Hide help panel
    hideHelp() {
        $('#helpPanel').addClass('hidden');
    }

    // Toggle fullscreen
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log(`Error attempting to enable fullscreen: ${err.message}`);
            });
        } else {
            document.exitFullscreen();
        }
    }



}

// Initialize the game when the page loads
$(document).ready(() => {
    window.freeCellGame = new FreeCell();
});
