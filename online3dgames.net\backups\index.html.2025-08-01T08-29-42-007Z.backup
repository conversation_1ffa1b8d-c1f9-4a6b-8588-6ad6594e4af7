<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bubble Float - Relaxing Bubble Animation</title>
    <meta name="description" content="Enjoy peaceful floating bubbles! Watch colorful bubbles gently float and pop in this relaxing interactive animation. Perfect for stress relief and meditation.">
    <meta name="keywords" content="bubble animation, floating bubbles, relaxation, stress relief, interactive bubbles, calm, peaceful, meditation, bubble pop">
    <meta name="author" content="Game Center">
    <meta property="og:title" content="Bubble Float - Relaxing Animation">
    <meta property="og:description" content="Relax with beautiful floating bubbles in this peaceful interactive experience.">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Bubble Float - Peaceful Bubbles">
    <meta name="twitter:description" content="Find peace with gently floating bubbles in this calming interactive animation.">
    <link rel="canonical" href="/bubble-float">
    <link rel="stylesheet" href="/bubble-float/styles/style.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🫧 Bubble Float</h1>
            <button id="home-btn" class="home-btn">Home</button>
        </header>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="bubble-settings">
                <div class="setting-group">
                    <label>Bubble Density</label>
                    <input type="range" id="bubble-density" min="5" max="50" value="20">
                    <span id="density-display">20</span>
                </div>
                <div class="setting-group">
                    <label>Float Speed</label>
                    <input type="range" id="float-speed" min="0.5" max="3" step="0.1" value="1">
                    <span id="speed-display">1x</span>
                </div>
                <div class="setting-group">
                    <label>Bubble Size</label>
                    <input type="range" id="bubble-size" min="0.5" max="2" step="0.1" value="1">
                    <span id="size-display">1x</span>
                </div>
            </div>
            
            <div class="effect-modes">
                <button id="classic-mode" class="mode-btn active">🌊 Classic</button>
                <button id="colorful-mode" class="mode-btn">🌈 Colorful</button>
                <button id="neon-mode" class="mode-btn">💫 Neon</button>
                <button id="gentle-mode" class="mode-btn">🕊️ Gentle</button>
            </div>
            
            <div class="action-controls">
                <button id="pause-btn" class="control-btn secondary">⏸️ Pause</button>
                <button id="reset-btn" class="control-btn primary">🔄 Reset</button>
                <button id="fullscreen-btn" class="control-btn success">🖥️ Fullscreen</button>
            </div>
        </div>

        <!-- Bubble Container -->
        <div class="bubble-container" id="bubble-container">
            <div id="game-start" class="game-overlay">
                <div class="game-start-content">
                    <h2>🫧 Bubble Float</h2>
                    <p>Watch bubbles float up, relax your mind</p>
                    <p class="tip">💡 Mouse over will create more bubbles</p>
                    <button id="canvas-start-btn" class="control-btn primary">Start Relaxing</button>
                </div>
            </div>
            
            <svg id="bubble-svg" width="100%" height="100%">
                <defs>
                    <radialGradient id="bubbleGradient" cx="30%" cy="30%">
                        <stop offset="0%" style="stop-color:rgba(255,255,255,0.8)"/>
                        <stop offset="70%" style="stop-color:rgba(255,255,255,0.4)"/>
                        <stop offset="100%" style="stop-color:rgba(255,255,255,0.1)"/>
                    </radialGradient>
                    <filter id="glow">
                        <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                        <feMerge> 
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>
                    <filter id="blur">
                        <feGaussianBlur in="SourceGraphic" stdDeviation="1"/>
                    </filter>
                </defs>
            </svg>
            
            <div class="interaction-hint">Move mouse to create more bubbles</div>
        </div>

        <!-- Instructions -->
        <div class="instructions">
            <h3>Instructions</h3>
            <div class="instruction-grid">
                <div class="instruction-item">
                    <span class="icon">🫧</span>
                    <div class="desc">
                        <strong>Watch Bubbles</strong><br>
                        Enjoy the process of bubbles floating up
                    </div>
                </div>
                <div class="instruction-item">
                    <span class="icon">🖱️</span>
                    <div class="desc">
                        <strong>Mouse Interaction</strong><br>
                        Move mouse to create more bubbles
                    </div>
                </div>
                <div class="instruction-item">
                    <span class="icon">🎨</span>
                    <div class="desc">
                        <strong>Multiple Modes</strong><br>
                        Classic, Colorful, Neon
                    </div>
                </div>
                <div class="instruction-item">
                    <span class="icon">⚙️</span>
                    <div class="desc">
                        <strong>Custom Settings</strong><br>
                        Adjust density and speed
                    </div>
                </div>
            </div>
            
            <div class="tips">
                <h4>Relaxation Tips</h4>
                <ul>
                    <li>Focus on observing the bubbles' rising trajectory</li>
                    <li>Relax your mind with deep breathing</li>
                    <li>Try different visual modes</li>
                    <li>Fullscreen for better experience</li>
                    <li>Adjust the speed to find the most comfortable rhythm</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section" style="max-width: 1200px; margin: 40px auto; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6; position: relative; z-index: 1; border-radius: 10px;">
        <h2 style="color: #ffffff; margin-bottom: 20px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">Bubble Float - Peaceful Relaxation Experience</h2>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">💭 Meditative Bubble Experience</h3>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Immerse yourself in the tranquil world of floating bubbles designed for deep relaxation and mindfulness. Watch as colorful bubbles gently rise and dance across your screen in mesmerizing patterns.</p>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Features beautiful gradient colors, smooth animations, and interactive elements that respond to your mouse movements for a personalized relaxation experience.</p>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🌈 Relaxation Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li><strong style="color: #ffffff;">Interactive Bubbles:</strong> Mouse movement creates more bubbles</li>
                    <li><strong style="color: #ffffff;">Smooth Animation:</strong> Fluid bubble physics</li>
                    <li><strong style="color: #ffffff;">Color Gradients:</strong> Beautiful visual effects</li>
                    <li><strong style="color: #ffffff;">Endless Experience:</strong> Continuous bubble generation</li>
                    <li><strong style="color: #ffffff;">Stress Relief:</strong> Proven calming benefits</li>
                </ul>
            </div>
        </div>

        <div style="margin-bottom: 30px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🧘 Mindfulness and Meditation</h3>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Bubble Float is designed as a digital meditation tool that helps reduce stress and promote mindfulness. The gentle movement and soft colors create a calming environment perfect for relaxation.</p>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Use this experience during breaks, before sleep, or anytime you need a moment of peace and tranquility in your busy day.</p>
        </div>

        <div style="padding: 20px; margin-bottom: 20px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🌟 Your Digital Zen Garden</h3>
            <p style="margin-bottom: 10px; color: #f0f0f0;">Create your own peaceful bubble sanctuary with interactive elements that respond to your presence. Perfect for meditation, stress relief, or simply enjoying beautiful visuals.</p>
            <p style="color: #f0f0f0;">Let the gentle floating bubbles wash away your stress and bring a sense of calm to your mind and spirit!</p>
        </div>
    </div>

                            <!-- Game Recommendations -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">More Relaxation Games</h3>
            <p class="recommendations-subtitle">Continue exploring relaxation games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/pop-it-game" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🫧</div>
                        <h4 class="game-name">Pop-it</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/fidget-spinner" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌀</div>
                        <h4 class="game-name">Fidget Spinner</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
                <a href="/breathing-ball" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌬️</div>
                        <h4 class="game-name">Breathing Ball</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/drawing-wall" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🎨</div>
                        <h4 class="game-name">Drawing Wall</h4>
                        <div class="game-rating">⭐ 4.2</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">Other Games</h3>
            <p class="recommendations-subtitle">Discover more amazing games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/2048" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🔢</div>
                        <h4 class="game-name">2048</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎰</div>
                        <h4 class="game-name">Blackjack</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/blackjack-practice" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎯</div>
                        <h4 class="game-name">Blackjack Practice</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/freeBetBlackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🆓</div>
                        <h4 class="game-name">Free Bet Blackjack</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/pontoon-game" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🚤</div>
                        <h4 class="game-name">Pontoon</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/hearts" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♥️</div>
                        <h4 class="game-name">Hearts</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">Solitaire</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/spider-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🕷️</div>
                        <h4 class="game-name">Spider Solitaire</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/texas-holdem-game" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🎲</div>
                        <h4 class="game-name">Texas Hold'em</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row">
                <a href="/spades" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♠️</div>
                        <h4 class="game-name">Spades</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/chess" class="recommendation-card" data-category="strategy">
                    <div class="card-image">
                        <div class="game-icon">♔</div>
                        <h4 class="game-name">Chess</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/sudoku-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧩</div>
                        <h4 class="game-name">Sudoku</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/tetris-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🟦</div>
                        <h4 class="game-name">Tetris</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/memoryGame" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧠</div>
                        <h4 class="game-name">Memory Game</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/snake-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🐍</div>
                        <h4 class="game-name">Snake</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/breakout-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🧱</div>
                        <h4 class="game-name">Breakout</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/reaction-test" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">⚡</div>
                        <h4 class="game-name">Reaction Test</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/particle-trail" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">✨</div>
                        <h4 class="game-name">Particle Trail</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
                <a href="/typing-machine" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">⌨️</div>
                        <h4 class="game-name">Typing Machine</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
                <a href="/virtual-pet" class="recommendation-card" data-category="other">
                    <div class="card-image">
                        <div class="game-icon">🐱</div>
                        <h4 class="game-name">Virtual Pet</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
    <script src="/bubble-float/src/bubble-float.js"></script>
    <script src="/bubble-float/src/game.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>