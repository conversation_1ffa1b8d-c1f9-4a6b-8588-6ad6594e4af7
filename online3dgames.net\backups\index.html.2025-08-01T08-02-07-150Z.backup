<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pontoon - English 21 Card Game</title>
    <meta name="description" content="Play Pontoon (English 21) online for free. Classic British card game with Twist, Stick, and special Five Card Trick rules.">
    <meta name="keywords" content="pontoon, english 21, card game, online game, free game, british blackjack">
    <link rel="stylesheet" href="/pontoon-game/css/styles.css">
    <link rel="stylesheet" href="/assets/css/game-recommendations.css">
</head>
<body>

    <!-- SEO H1 for search engines (hidden from users) -->
    <h1 style="position: absolute; left: -9999px; top: -9999px; visibility: hidden;">Pontoon - English 21 Card Game Online</h1>

    <div class="casino-container">
        <div class="top-status">
            <div class="right-controls" style="margin-left: auto;">
                <button class="home-button" id="home-button" title="Go to Home">
                    <span class="home-icon">🏠</span>
                </button>
                <button class="fullscreen-button" id="fullscreen-button" title="Toggle Fullscreen">
                    <span class="fullscreen-icon">⛶</span>
                </button>

                <button class="settings-button" id="settings-button" title="Game Settings">
                    <span class="settings-icon">⚙️</span>
                </button>
            </div>
        </div>

        <div class="casino-table">
            <div class="dealer-section">
            <div class="dealer-info">
                <div class="dealer-title">Dealer</div>
                <div class="dealer-score">
                    <span id="dealer-score">0</span>
                </div>
            </div>
            <div class="dealer-cards-container">
                <div class="dealer-cards-area" id="dealer-cards">
                </div>
            </div>
        </div>

        <div class="players-area">
            <div class="player-position current-player" data-position="0">
                <div class="player-cards-container">
                    <div class="player-cards-area" id="player-cards-0">
                    </div>
                    <div class="player-score hidden">
                        <span id="player-score-0">0</span>
                    </div>
                </div>
                <div class="bet-spot">
                    <div class="bet-circle active" data-position="0">
                        <div class="bet-amount" id="bet-amount-0">0</div>
                    </div>
                </div>
                <div class="player-info">
                </div>
                <div class="player-balance"><span id="player-balance-0">1000</span></div>
            </div>
        </div>
        </div>

        <div class="table-center">
            <div class="game-status" id="game-status">Welcome to Pontoon! Place your bet to start playing.</div>
        </div>

        <div class="bottom-controls">
            <!-- Game action buttons - always visible -->
            <div class="game-actions-section">
                <button id="hint" class="action-btn hint-btn">
                    <span class="btn-icon">💡</span>
                    <span class="btn-text">Strategy</span>
                </button>
                <button id="double-down" class="action-btn double-btn">
                    <span class="btn-icon">💰</span>
                    <span class="btn-text">Buy</span>
                </button>
                <button id="split" class="action-btn split-btn">
                    <span class="btn-icon">⚡</span>
                    <span class="btn-text">Split</span>
                </button>
                <button id="twist" class="action-btn hit-btn">
                    <span class="btn-icon">+</span>
                    <span class="btn-text">Twist</span>
                </button>
                <button id="stick" class="action-btn stand-btn">
                    <span class="btn-icon">■</span>
                    <span class="btn-text">Stick</span>
                </button>
            </div>
            <div class="chip-section">
                <div class="chip-tray">
                    <!-- Chips will be dynamically generated -->
                </div>
            </div>

            <div class="betting-section">
                <button id="clear-bet" class="action-btn clear-btn" title="Clear">
                    Clear
                </button>
                <button id="deal-cards" class="action-btn deal-btn" title="Done" disabled>
                    <span class="btn-text">Done</span>
                </button>
            </div>
        </div>

        <div class="deck-area">
            <div class="deck-container" id="main-deck">
                <div class="deck-pile">
                    <div class="card card-back deck-card"></div>
                    <div class="deck-info">
                        <div class="deck-label">Cards Left</div>
                        <div class="deck-count" id="deck-count">312</div>
                        <div class="deck-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" id="deck-progress-fill"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="settings-modal" id="settings-modal" style="display: none;">
        <div class="settings-modal-content">
            <div class="settings-header">
                <h2>Game Settings</h2>
                <button class="settings-close" id="settings-close">&times;</button>
            </div>
            <div class="settings-body">
                <div class="settings-section">
                    <h3>🃏 Number of Decks</h3>
                    <p>Choose how many decks to play with. More decks make card counting harder.</p>
                    <div class="current-settings">
                        <div class="current-setting-item">
                            <span class="setting-label">Currently Active:</span>
                            <span class="setting-value" id="current-deck-count">6 Decks</span>
                        </div>
                    </div>
                    <div class="deck-options">
                        <button class="deck-option-btn" data-decks="2">
                            <div class="deck-option-title">2 Decks</div>
                            <div class="deck-option-desc">Double deck</div>
                        </button>
                        <button class="deck-option-btn active" data-decks="6">
                            <div class="deck-option-title">6 Decks</div>
                            <div class="deck-option-desc">Casino standard</div>
                        </button>
                        <button class="deck-option-btn" data-decks="8">
                            <div class="deck-option-title">8 Decks</div>
                            <div class="deck-option-desc">Professional casino</div>
                        </button>
                    </div>
                    <div class="settings-notice" id="settings-notice" style="display: none;">
                        <div class="notice-icon">ℹ️</div>
                        <div class="notice-text">Settings will take effect in the next round</div>
                    </div>
                </div>
            </div>
            <div class="settings-footer">
                <button id="apply-settings" class="apply-settings-btn">Apply Settings</button>
            </div>
        </div>
    </div>



    <div id="game-over-modal" class="modal" style="display: none;">
        <div class="modal-content game-over-content">
            <div class="game-over-header">
                <h2>Game Over</h2>
                <div class="game-over-icon">💸</div>
            </div>
            <div class="game-over-message">
                <p class="main-message">Your balance has run out!</p>
                <p class="encouragement-message">Don't worry, every great player has faced setbacks. The key to success in Pontoon is learning from experience and managing your bankroll wisely.</p>
                <p class="tip-message">💡 <strong>Pro Tip:</strong> Start with smaller bets and gradually increase as you build your confidence and skills!</p>
            </div>
            <div class="game-over-actions">
                <button id="restart-game-btn" class="restart-btn">
                    <span class="restart-icon">🎰</span>
                    Start Fresh
                </button>
            </div>
        </div>
    </div>

    <!-- SEO Content Section -->
    <div class="seo-content-section" style="max-width: 1200px; margin: 40px auto; padding: 20px; font-family: Arial, sans-serif; line-height: 1.6; border-radius: 10px;">
        <h2 style="color: #ffffff; margin-bottom: 20px; font-size: 28px; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">Master Pontoon - The Ultimate English 21 Card Game Experience</h2>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎯 What Makes Pontoon Special</h3>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Pontoon is the British cousin of Blackjack, offering unique gameplay mechanics that set it apart from traditional 21 games. Unlike American Blackjack, Pontoon features the exciting "Five Card Trick" rule and requires players to achieve higher scores to beat the dealer.</p>
                <p style="margin-bottom: 15px; color: #f0f0f0;">Our authentic Pontoon implementation includes all traditional English casino rules, making it the perfect choice for players seeking a genuine British card game experience online.</p>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🏆 Key Pontoon Features</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li><strong style="color: #ffffff;">Five Card Trick:</strong> Win with 5 cards totaling 21 or less</li>
                    <li><strong style="color: #ffffff;">Pontoon Hand:</strong> Ace + 10-value card pays 2:1</li>
                    <li><strong style="color: #ffffff;">Twist & Stick:</strong> Traditional British terminology</li>
                    <li><strong style="color: #ffffff;">Buy Option:</strong> Double your bet for one more card (2-4 cards)</li>
                    <li><strong style="color: #ffffff;">Multiple Decks:</strong> Choose from 2, 6, or 8 deck games</li>
                </ul>
            </div>
        </div>

        <div style="margin-bottom: 30px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎮 How to Play Pontoon Online</h3>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Playing Pontoon online is straightforward yet thrilling. Start by placing your bet using our intuitive chip system, then receive your initial two cards. The goal is to get as close to 21 as possible without going over, but remember - in Pontoon, ties go to the dealer!</p>
            <p style="margin-bottom: 15px; color: #f0f0f0;">Use "Twist" to take another card or "Stick" when you're satisfied with your hand (minimum 15 required). The "Buy" option lets you double your bet for exactly one more card and is available when you have 2, 3, or 4 cards. When splitting identical cards, each split hand must draw at least one additional card before you can stick, following authentic Pontoon rules.</p>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 30px;">
            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">💰 Pontoon Payout Structure</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li><strong style="color: #ffffff;">Pontoon (Natural 21):</strong> 2:1 payout</li>
                    <li><strong style="color: #ffffff;">Five Card Trick:</strong> 2:1 payout</li>
                    <li><strong style="color: #ffffff;">Regular Win:</strong> 1:1 payout</li>
                    <li><strong style="color: #ffffff;">Dealer Tie:</strong> Dealer wins</li>
                </ul>
            </div>

            <div>
                <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 22px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🎯 Winning Strategies</h3>
                <ul style="margin-bottom: 15px; padding-left: 20px; color: #f0f0f0;">
                    <li>Always twist on hands below 15</li>
                    <li>Consider the Five Card Trick opportunity</li>
                    <li>Manage your bankroll with strategic betting</li>
                    <li>Learn when to buy effectively (2-4 cards)</li>
                </ul>
            </div>
        </div>

        <div style="padding: 20px; margin-bottom: 20px;">
            <h3 style="color: #ffffff; margin-bottom: 15px; font-size: 20px; text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">🌟 Why Choose Our Pontoon Game?</h3>
            <p style="margin-bottom: 10px; color: #f0f0f0;">Experience the most authentic Pontoon game available online, featuring smooth animations, realistic casino sounds, and professional-grade graphics. Our game supports multiple deck configurations and includes helpful strategy hints to improve your gameplay.</p>
            <p style="color: #f0f0f0;">Whether you're a seasoned Pontoon player or new to this exciting British card game, our user-friendly interface and comprehensive rules make it easy to jump in and start winning. Play for free with virtual chips and master the art of English 21!</p>
        </div>
    </div>

    <!-- Static Game Recommendations for SEO -->
    <div class="game-recommendations similar-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">More Blackjack Zone</h3>
            <p class="recommendations-subtitle">Continue exploring similar games</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/blackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎰</div>
                        <h4 class="game-name">Blackjack</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/blackjack-practice" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🎯</div>
                        <h4 class="game-name">Blackjack Practice</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/freeBetBlackjack" class="recommendation-card" data-category="blackjack">
                    <div class="card-image">
                        <div class="game-icon">🆓</div>
                        <h4 class="game-name">Free Bet Blackjack</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <div class="game-recommendations other-games">
        <div class="recommendations-header">
            <h3 class="recommendations-title">Explore Other Games</h3>
            <p class="recommendations-subtitle">Discover different types of entertainment</p>
        </div>
        <div class="recommendations-grid">
            <div class="recommendations-row">
                <a href="/hearts" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">♥️</div>
                        <h4 class="game-name">Hearts</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/sudoku-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🧩</div>
                        <h4 class="game-name">Sudoku</h4>
                        <div class="game-rating">⭐ 4.8</div>
                    </div>
                </a>
                <a href="/snake-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🐍</div>
                        <h4 class="game-name">Snake</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/pop-it-game" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🫧</div>
                        <h4 class="game-name">Pop-it</h4>
                        <div class="game-rating">⭐ 4.4</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row offset-row">
                <a href="/solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🃏</div>
                        <h4 class="game-name">Solitaire</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
                <a href="/tetris-game" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🟦</div>
                        <h4 class="game-name">Tetris</h4>
                        <div class="game-rating">⭐ 4.9</div>
                    </div>
                </a>
                <a href="/breakout-game" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">🧱</div>
                        <h4 class="game-name">Breakout</h4>
                        <div class="game-rating">⭐ 4.6</div>
                    </div>
                </a>
            </div>
            <div class="recommendations-row">
                <a href="/spider-solitaire" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🕷️</div>
                        <h4 class="game-name">Spider Solitaire</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/texas-holdem-game" class="recommendation-card" data-category="card-games">
                    <div class="card-image">
                        <div class="game-icon">🎲</div>
                        <h4 class="game-name">Texas Hold'em</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/2048" class="recommendation-card" data-category="puzzle">
                    <div class="card-image">
                        <div class="game-icon">🔢</div>
                        <h4 class="game-name">2048</h4>
                        <div class="game-rating">⭐ 4.7</div>
                    </div>
                </a>
                <a href="/reaction-test" class="recommendation-card" data-category="arcade">
                    <div class="card-image">
                        <div class="game-icon">⚡</div>
                        <h4 class="game-name">Reaction Test</h4>
                        <div class="game-rating">⭐ 4.5</div>
                    </div>
                </a>
                <a href="/fidget-spinner" class="recommendation-card" data-category="relaxation">
                    <div class="card-image">
                        <div class="game-icon">🌀</div>
                        <h4 class="game-name">Fidget Spinner</h4>
                        <div class="game-rating">⭐ 4.3</div>
                    </div>
                </a>
            </div>
        </div>
    </div>

    <script src="/assets/js/jquery-3.7.1.min.js"></script>
    <script src="/pontoon-game/js/game.js"></script>
    <script src="/assets/js/game-recommendations.js"></script>
</body>
</html>